package logger

import (
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"go.uber.org/zap/zapcore"
)

// mockLogger implements Logger interface for testing
type mockLogger struct{}

func (m *mockLogger) Debug(msg string) {}
func (m *mockLogger) Info(msg string)  {}
func (m *mockLogger) Warn(msg string)  {}
func (m *mockLogger) Error(msg string) {}
func (m *mockLogger) Fatal(msg string) {}
func (m *mockLogger) Close()           {}

func TestLevel_String(t *testing.T) {
	tests := []struct {
		level    Level
		expected string
	}{
		{LevelDebug, "DEBUG  "},
		{LevelInfo, "INFO   "},
		{LevelWarning, "WARN   "},
		{LevelError, "ERROR  "},
		{LevelFatal, "FATAL  "},
		{Level(999), "UNKNOWN"},
	}

	for _, test := range tests {
		if got := test.level.String(); got != test.expected {
			t.<PERSON><PERSON><PERSON>("Level(%d).String() = %q, want %q", test.level, got, test.expected)
		}
	}
}

func TestParseLevel(t *testing.T) {
	tests := []struct {
		input    string
		expected Level
	}{
		{"DEBUG", LevelDebug},
		{"debug", LevelDebug},
		{"  DEBUG  ", LevelDebug},
		{"INFO", LevelInfo},
		{"info", LevelInfo},
		{"WARN", LevelWarning},
		{"WARNING", LevelWarning},
		{"warn", LevelWarning},
		{"ERROR", LevelError},
		{"error", LevelError},
		{"FATAL", LevelFatal},
		{"fatal", LevelFatal},
		{"INVALID", LevelInfo}, // Default case
		{"", LevelInfo},        // Default case
	}

	for _, test := range tests {
		if got := ParseLevel(test.input); got != test.expected {
			t.Errorf("ParseLevel(%q) = %v, want %v", test.input, got, test.expected)
		}
	}
}

func TestGetDefaultConfig(t *testing.T) {
	config := getDefaultConfig()

	if config.LogFolder != DefaultLogFolder {
		t.Errorf("LogFolder = %q, want %q", config.LogFolder, DefaultLogFolder)
	}
	if config.MaxSize != DefaultMaxSize {
		t.Errorf("MaxSize = %d, want %d", config.MaxSize, DefaultMaxSize)
	}
	if config.EnableConsole != DefaultEnableConsole {
		t.Errorf("EnableConsole = %v, want %v", config.EnableConsole, DefaultEnableConsole)
	}
	if config.AppName != DefaultAppName {
		t.Errorf("AppName = %q, want %q", config.AppName, DefaultAppName)
	}
	if config.EnableColors != DefaultEnableColors {
		t.Errorf("EnableColors = %v, want %v", config.EnableColors, DefaultEnableColors)
	}
	if config.Environment != Development {
		t.Errorf("Environment = %v, want %v", config.Environment, Development)
	}
	if config.MaxBackups != 7 {
		t.Errorf("MaxBackups = %d, want %d", config.MaxBackups, 7)
	}
	if config.MaxAge != 7 {
		t.Errorf("MaxAge = %d, want %d", config.MaxAge, 7)
	}
	if config.Verbose != false {
		t.Errorf("Verbose = %v, want %v", config.Verbose, false)
	}
	if config.Operation != "" {
		t.Errorf("Operation = %q, want %q", config.Operation, "")
	}
}

func TestDefaultConfigProvider(t *testing.T) {
	// Test with verbose=true
	provider := NewDefaultConfigProvider(true)
	config := provider.GetConfig()
	if !config.Verbose {
		t.Errorf("Expected verbose=true, got %v", config.Verbose)
	}

	// Test with verbose=false
	provider = NewDefaultConfigProvider(false)
	config = provider.GetConfig()
	if config.Verbose {
		t.Errorf("Expected verbose=false, got %v", config.Verbose)
	}
}

func TestDefaultEncoderFactory(t *testing.T) {
	factory := NewDefaultEncoderFactory()

	// Test console encoder with colors
	encoder := factory.CreateConsoleEncoder(true)
	if encoder == nil {
		t.Error("CreateConsoleEncoder(true) returned nil")
	}

	// Test console encoder without colors
	encoder = factory.CreateConsoleEncoder(false)
	if encoder == nil {
		t.Error("CreateConsoleEncoder(false) returned nil")
	}

	// Test file encoder
	encoder = factory.CreateFileEncoder()
	if encoder == nil {
		t.Error("CreateFileEncoder() returned nil")
	}
}

func TestDefaultWriterFactory(t *testing.T) {
	factory := NewDefaultWriterFactory()
	config := getDefaultConfig()
	config.Operation = "test"

	writer := factory.CreateFileWriter(config)
	if writer == nil {
		t.Error("CreateFileWriter() returned nil")
		return
	}

	// Check filename format
	expectedDate := time.Now().Format("2006-01-02")
	expectedFilename := filepath.Join(config.LogFolder, "mulberri-test-"+expectedDate+".log")
	if writer.Filename != expectedFilename {
		t.Errorf("Filename = %q, want %q", writer.Filename, expectedFilename)
	}
}

func TestCreateLumberjackWriter(t *testing.T) {
	config := getDefaultConfig()
	config.Operation = "unittest"
	config.LogFolder = "test_logs"

	writer := createLumberjackWriter(config)

	expectedDate := time.Now().Format("2006-01-02")
	expectedFilename := filepath.Join("test_logs", "mulberri-unittest-"+expectedDate+".log")

	if writer.Filename != expectedFilename {
		t.Errorf("Filename = %q, want %q", writer.Filename, expectedFilename)
	}
	if writer.MaxSize != int(config.MaxSize) {
		t.Errorf("MaxSize = %d, want %d", writer.MaxSize, int(config.MaxSize))
	}
	if writer.MaxBackups != config.MaxBackups {
		t.Errorf("MaxBackups = %d, want %d", writer.MaxBackups, config.MaxBackups)
	}
	if writer.MaxAge != config.MaxAge {
		t.Errorf("MaxAge = %d, want %d", writer.MaxAge, config.MaxAge)
	}
	if !writer.Compress {
		t.Error("Expected Compress = true")
	}

	// Test fallback operation
	config.Operation = ""
	writer = createLumberjackWriter(config)
	if !strings.Contains(writer.Filename, "mulberri-app-") {
		t.Errorf("Expected fallback operation 'app' in filename, got %q", writer.Filename)
	}
}

func TestCreateZapConfig(t *testing.T) {
	// Test development environment with verbose
	config := getDefaultConfig()
	config.Environment = Development
	config.Verbose = true
	config.EnableColors = true

	zapConfig := createZapConfig(config)
	if zapConfig.Level.Level() != zapcore.DebugLevel {
		t.Errorf("Expected DebugLevel for verbose=true, got %v", zapConfig.Level.Level())
	}
	if zapConfig.Encoding != "console" {
		t.Errorf("Expected console encoding for development, got %q", zapConfig.Encoding)
	}

	// Test production environment with non-verbose
	config.Environment = Production
	config.Verbose = false
	config.EnableColors = false

	zapConfig = createZapConfig(config)
	if zapConfig.Level.Level() != zapcore.InfoLevel {
		t.Errorf("Expected InfoLevel for verbose=false, got %v", zapConfig.Level.Level())
	}
	if zapConfig.Encoding != "json" {
		t.Errorf("Expected json encoding for production, got %q", zapConfig.Encoding)
	}

	// Test development without colors
	config.Environment = Development
	config.EnableColors = false

	zapConfig = createZapConfig(config)
	if zapConfig.Encoding != "console" {
		t.Errorf("Expected console encoding for development, got %q", zapConfig.Encoding)
	}
}

func TestLoggerFactory(t *testing.T) {
	configProvider := NewDefaultConfigProvider(false)
	encoderFactory := NewDefaultEncoderFactory()
	writerFactory := NewDefaultWriterFactory()

	factory := NewLoggerFactory(configProvider, encoderFactory, writerFactory)
	if factory == nil {
		t.Error("NewLoggerFactory returned nil")
	}

	// Create temporary log directory
	tempDir := "test_logs_factory"
	defer os.RemoveAll(tempDir)

	// Update config to use temp directory
	config := configProvider.GetConfig()
	config.LogFolder = tempDir
	config.Operation = "factorytest"
	configProvider = &DefaultConfigProvider{config: config}
	factory = NewLoggerFactory(configProvider, encoderFactory, writerFactory)

	logger, err := factory.CreateLogger()
	if err != nil {
		t.Fatalf("CreateLogger() failed: %v", err)
	}
	if logger == nil {
		t.Error("CreateLogger() returned nil logger")
	}

	logger.Close()
}

func TestNewLoggerWithVerbose(t *testing.T) {
	// Create temporary log directory
	tempDir := "test_logs_verbose"
	defer os.RemoveAll(tempDir)

	// Test with verbose=true
	logger, err := NewLoggerWithVerbose(true)
	if err != nil {
		t.Fatalf("NewLoggerWithVerbose(true) failed: %v", err)
	}
	if logger == nil {
		t.Error("NewLoggerWithVerbose(true) returned nil")
	}
	logger.Close()

	// Test with verbose=false
	logger, err = NewLoggerWithVerbose(false)
	if err != nil {
		t.Fatalf("NewLoggerWithVerbose(false) failed: %v", err)
	}
	if logger == nil {
		t.Error("NewLoggerWithVerbose(false) returned nil")
	}
	logger.Close()
}

func TestZapLoggerMethods(t *testing.T) {
	// Create temporary log directory
	tempDir := "test_logs_methods"
	defer os.RemoveAll(tempDir)

	config := getDefaultConfig()
	config.LogFolder = tempDir
	config.Operation = "methodtest"
	config.Verbose = true

	logger, err := NewWithConfigAndFactories(config, NewDefaultEncoderFactory(), NewDefaultWriterFactory())
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	// Test all logging methods
	logger.Debug("Debug message")
	logger.Info("Info message")
	logger.Warn("Warn message")
	logger.Error("Error message")

	logger.Close()
}

func TestInitForOperations(t *testing.T) {
	// Create temporary log directory
	tempDir := "test_logs_operations"
	defer os.RemoveAll(tempDir)

	// Test InitForTrain
	err := InitForTrain(true)
	if err != nil {
		t.Errorf("InitForTrain(true) failed: %v", err)
	}

	// Test InitForPredict
	err = InitForPredict(false)
	if err != nil {
		t.Errorf("InitForPredict(false) failed: %v", err)
	}

	CloseGlobal()
}

func TestGlobalLoggerFunctions(t *testing.T) {
	// Test basic global functions
	Debug("Test debug message")
	Info("Test info message")
	Warn("Test warn message")
	Error("Test error message")

	// Test operation-specific functions
	TrainDebug("Train debug message")
	TrainInfo("Train info message")
	TrainWarn("Train warn message")
	TrainError("Train error message")

	PredictDebug("Predict debug message")
	PredictInfo("Predict info message")
	PredictWarn("Predict warn message")
	PredictError("Predict error message")

	CloseGlobal()
}

func TestSetGlobalConfig(t *testing.T) {
	config := getDefaultConfig()
	config.Operation = "globaltest"
	config.Verbose = true

	err := SetGlobalConfig(config)
	if err != nil {
		t.Errorf("SetGlobalConfig failed: %v", err)
	}

	CloseGlobal()
}

func TestSetGlobalConfigWithVerbose(t *testing.T) {
	err := SetGlobalConfigWithVerbose(true)
	if err != nil {
		t.Errorf("SetGlobalConfigWithVerbose(true) failed: %v", err)
	}

	err = SetGlobalConfigWithVerbose(false)
	if err != nil {
		t.Errorf("SetGlobalConfigWithVerbose(false) failed: %v", err)
	}

	CloseGlobal()
}

func TestInitWithVerbose(t *testing.T) {
	err := InitWithVerbose(true)
	if err != nil {
		t.Errorf("InitWithVerbose(true) failed: %v", err)
	}

	err = InitWithVerbose(false)
	if err != nil {
		t.Errorf("InitWithVerbose(false) failed: %v", err)
	}

	CloseGlobal()
}

func TestIsVerboseEnabled(t *testing.T) {
	// Test with no global logger
	CloseGlobal()
	globalLogger = nil
	if IsVerboseEnabled() {
		t.Error("Expected IsVerboseEnabled() to return false when no global logger")
	}

	// Test with verbose enabled
	InitWithVerbose(true)
	if !IsVerboseEnabled() {
		t.Error("Expected IsVerboseEnabled() to return true after InitWithVerbose(true)")
	}

	// Test with verbose disabled
	InitWithVerbose(false)
	if IsVerboseEnabled() {
		t.Error("Expected IsVerboseEnabled() to return false after InitWithVerbose(false)")
	}

	CloseGlobal()
}

func TestNewWithConfigAndFactories(t *testing.T) {
	tempDir := "test_logs_config_factories"
	defer os.RemoveAll(tempDir)

	config := getDefaultConfig()
	config.LogFolder = tempDir
	config.Operation = "configtest"
	config.EnableConsole = false // Test without console output

	encoderFactory := NewDefaultEncoderFactory()
	writerFactory := NewDefaultWriterFactory()

	logger, err := NewWithConfigAndFactories(config, encoderFactory, writerFactory)
	if err != nil {
		t.Fatalf("NewWithConfigAndFactories failed: %v", err)
	}

	logger.Info("Test message")
	logger.Close()

	// Verify log file was created
	expectedDate := time.Now().Format("2006-01-02")
	logFile := filepath.Join(tempDir, "mulberri-configtest-"+expectedDate+".log")
	if _, err := os.Stat(logFile); os.IsNotExist(err) {
		t.Errorf("Log file %s was not created", logFile)
	}
}

func TestErrorCases(t *testing.T) {
	// Test with invalid log folder (permission denied)
	config := getDefaultConfig()
	config.LogFolder = "/root/invalid_folder" // Should fail on most systems
	config.Operation = "errortest"

	_, err := NewWithConfigAndFactories(config, NewDefaultEncoderFactory(), NewDefaultWriterFactory())
	if err == nil {
		t.Error("Expected error when creating logger with invalid log folder")
	}
}

func TestZapLoggerClose(t *testing.T) {
	// Test closing logger with nil zapLogger
	logger := &ZapLogger{
		zapLogger: nil,
	}
	logger.Close() // Should not panic

	// Test normal close
	config := getDefaultConfig()
	config.LogFolder = "test_logs_close"
	config.Operation = "closetest"
	defer os.RemoveAll("test_logs_close")

	logger2, err := NewWithConfigAndFactories(config, NewDefaultEncoderFactory(), NewDefaultWriterFactory())
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}
	logger2.Close() // Should not panic
}

func TestInitGlobalLoggerWithConfig(t *testing.T) {
	config := getDefaultConfig()
	config.Operation = "initglobaltest"
	config.Verbose = true

	initGlobalLoggerWithConfig(config)

	// Test that global logger was initialized
	if globalLogger == nil {
		t.Error("Expected global logger to be initialized")
	}

	CloseGlobal()
}

func TestGetGlobalLogger(t *testing.T) {
	// Reset global logger
	CloseGlobal()
	globalLogger = nil

	// Test lazy initialization
	logger := getGlobalLogger()
	if logger == nil {
		t.Error("getGlobalLogger() returned nil")
	}

	// Test that subsequent calls return the same logger
	logger2 := getGlobalLogger()
	if logger != logger2 {
		t.Error("getGlobalLogger() should return the same instance")
	}

	CloseGlobal()
}

func TestEnvironmentTypes(t *testing.T) {
	// Test Environment type values
	if Development != "development" {
		t.Errorf("Development = %q, want %q", Development, "development")
	}
	if Production != "production" {
		t.Errorf("Production = %q, want %q", Production, "production")
	}
}

func TestOperationConstants(t *testing.T) {
	// Test operation constants
	if OperationTrain != "train" {
		t.Errorf("OperationTrain = %q, want %q", OperationTrain, "train")
	}
	if OperationPredict != "predict" {
		t.Errorf("OperationPredict = %q, want %q", OperationPredict, "predict")
	}
}

func TestDefaultConstants(t *testing.T) {
	// Test default constants
	if DefaultLogFolder != "logs" {
		t.Errorf("DefaultLogFolder = %q, want %q", DefaultLogFolder, "logs")
	}
	if DefaultMaxSize != 10 {
		t.Errorf("DefaultMaxSize = %d, want %d", DefaultMaxSize, 10)
	}
	if DefaultEnableConsole != true {
		t.Errorf("DefaultEnableConsole = %v, want %v", DefaultEnableConsole, true)
	}
	if DefaultAppName != "mulberri" {
		t.Errorf("DefaultAppName = %q, want %q", DefaultAppName, "mulberri")
	}
	if DefaultEnableColors != true {
		t.Errorf("DefaultEnableColors = %v, want %v", DefaultEnableColors, true)
	}
}

func TestConfigProviderGetConfig(t *testing.T) {
	config := getDefaultConfig()
	config.Verbose = true
	config.Operation = "providertest"

	provider := &DefaultConfigProvider{config: config}
	retrievedConfig := provider.GetConfig()

	if retrievedConfig.Verbose != config.Verbose {
		t.Errorf("Retrieved config Verbose = %v, want %v", retrievedConfig.Verbose, config.Verbose)
	}
	if retrievedConfig.Operation != config.Operation {
		t.Errorf("Retrieved config Operation = %q, want %q", retrievedConfig.Operation, config.Operation)
	}
}

func TestIsVerboseEnabledWithNonZapLogger(t *testing.T) {
	// Create a mock logger that doesn't implement ZapLogger
	var mock mockLogger

	// Set global logger to mock
	globalMu.Lock()
	globalLogger = &mock
	globalMu.Unlock()

	// Should return false for non-ZapLogger
	if IsVerboseEnabled() {
		t.Error("Expected IsVerboseEnabled() to return false for non-ZapLogger")
	}

	CloseGlobal()
}
