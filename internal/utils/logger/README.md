# Logger Package - SOLID Compliant Design

A structured logging system for Mulberri following SOLID principles with operation-specific logging, date-based log files, automatic rotation, and clear separation of concerns.

## Features

- **Operation-Specific Logging**: Separate functions for training and prediction operations
- **Date-Based Log Files**: Daily log files with automatic rotation
- **SOLID Architecture**: Dependency injection and extensible design
- **Automatic Log Management**: Size-based rotation, backup retention, and compression
- **Verbose Control**: Debug level logging controlled by CLI flags
- **Thread-Safe**: Concurrent access from multiple goroutines

## Architecture Overview

The logger package follows SOLID principles:

- **Single Responsibility**: Each component has one clear purpose
- **Open/Closed**: Extensible through interfaces without modifying existing code
- **Liskov Substitution**: All implementations can be substituted seamlessly
- **Interface Segregation**: Small, focused interfaces
- **Dependency Inversion**: Depends on abstractions, not concrete implementations

## Core Interfaces

### Logger Interface
```go
type Logger interface {
    Debug(msg string)
    Info(msg string)
    Warn(msg string)
    Error(msg string)
    Fatal(msg string)
    Close()
}
```

### ConfigProvider Interface
```go
type ConfigProvider interface {
    GetConfig() Config
}
```

### EncoderFactory Interface
```go
type EncoderFactory interface {
    CreateConsoleEncoder(enableColors bool) zapcore.Encoder
    CreateFileEncoder() zapcore.Encoder
}
```

### WriterFactory Interface
```go
type WriterFactory interface {
    CreateFileWriter(config Config) *lumberjack.Logger
}
```

## Log File Organization

The logger creates operation-specific log files in the `logs/` directory:

```
logs/
├── mulberri-train-2025-08-11.log      # Today's training logs
├── mulberri-train-2025-08-11.log.1.gz # Rotated training backup
├── mulberri-predict-2025-08-11.log    # Today's prediction logs
├── mulberri-predict-2025-08-11.log.1.gz # Rotated prediction backup
└── ...
```

**Rotation Settings:**
- **MaxSize**: 10MB per file (rotates when reached)
- **MaxBackups**: 7 backup files kept
- **MaxAge**: 30 days retention
- **Compression**: Backup files are compressed

## Usage Patterns

### 1. Training Operations
```go
import "github.com/berrijam/mulberri/internal/utils/logger"

func main() {
    // Initialize for training (creates logs/mulberri-train-YYYY-MM-DD.log)
    logger.InitForTrain(cli.verbose)

    // Use training-specific functions (automatically prefixed with [TRAIN])
    logger.TrainInfo("Training session started")
    logger.TrainDebug("Loading dataset with 10,000 samples") // Only shows if verbose=true
    logger.TrainWarn("Learning rate might be too high")
    logger.TrainError("Failed to save checkpoint")

    // Regular functions still work (no prefix)
    logger.Info("General application message")
}
```

### 2. Prediction Operations
```go
func main() {
    // Initialize for prediction (creates logs/mulberri-predict-YYYY-MM-DD.log)
    logger.InitForPredict(cli.verbose)

    // Use prediction-specific functions (automatically prefixed with [PREDICT])
    logger.PredictInfo("Prediction session started")
    logger.PredictDebug("Model confidence: 0.95") // Only shows if verbose=true
    logger.PredictWarn("Low confidence for sample ID 123")
    logger.PredictError("Failed to process sample")

    // Regular functions still work (no prefix)
    logger.Info("General application message")
}
```

### 3. Simple Usage (Backward Compatible)
```go
func main() {
    // No initialization needed - works exactly as before
    logger.Info("Application started")
    logger.Debug("Debug message") // Won't show (default: info level)
    logger.Warn("Warning message")
    logger.Error("Error message")
}
```



### 4. Custom Configuration with Factories
```go
func main() {
    // Create custom components
    configProvider := logger.NewDefaultConfigProvider(false)
    encoderFactory := logger.NewDefaultEncoderFactory()
    writerFactory := logger.NewDefaultWriterFactory()

    // Inject dependencies
    factory := logger.NewLoggerFactory(configProvider, encoderFactory, writerFactory)
    customLogger, err := factory.CreateLogger()
    if err != nil {
        log.Fatal(err)
    }
    defer customLogger.Close()

    customLogger.Info("Custom logger with injected dependencies")
}
```

## Available Logging Functions

### Training-Specific Functions
```go
logger.TrainDebug("Loading training dataset")    
logger.TrainInfo("Epoch 1 completed")            
logger.TrainWarn("Learning rate too high")       
logger.TrainError("Checkpoint save failed")      
logger.TrainFatal("Critical training error")     
```

### Prediction-Specific Functions
```go
logger.PredictDebug("Model confidence: 0.95")    
logger.PredictInfo("Processing 1000 samples")   
logger.PredictWarn("Low confidence detected")   
logger.PredictError("Sample processing failed") 
logger.PredictFatal("Critical prediction error") 
```

### General Functions (Backward Compatible)
```go
logger.Debug("Debug message")    
logger.Info("Info message")      
logger.Warn("Warning message")   
logger.Error("Error message")    
logger.Fatal("Fatal message")    
```

## Extending the Logger

### Custom Encoder Factory
```go
type MyEncoderFactory struct{}

func (ef *MyEncoderFactory) CreateConsoleEncoder(enableColors bool) zapcore.Encoder {
    // Your custom console encoder implementation
}

func (ef *MyEncoderFactory) CreateFileEncoder() zapcore.Encoder {
    // Your custom file encoder implementation
}

// Use it:
factory := logger.NewLoggerFactory(
    logger.NewDefaultConfigProvider(true),
    &MyEncoderFactory{},
    logger.NewDefaultWriterFactory(),
)
```


## CLI Integration

The logger is designed to work seamlessly with your CLI package:

```go
// In your CLI package
func main() {
    verbose := parseVerboseFlag() // Your CLI parsing logic

    // For training commands
    if isTrainCommand {
        logger.InitForTrain(verbose)
        // Use logger.TrainInfo(), logger.TrainError(), etc.
    }

    // For prediction commands
    if isPredictCommand {
        logger.InitForPredict(verbose)
        // Use logger.PredictInfo(), logger.PredictError(), etc.
    }

    // Regular logging still works
    logger.Info("Application started")
}
```

## Log File Examples

### Training Log Output
```
2025-08-11 19:30:56| INFO |main.go:18 | Training session started
2025-08-11 19:30:56| DEBUG|main.go:19 | Loading dataset with 10,000 samples
2025-08-11 19:30:56| WARN |main.go:21 | Learning rate might be too high
2025-08-11 19:30:56| ERROR|main.go:23 | Failed to save checkpoint at epoch 1
```

### Prediction Log Output
```
2025-08-11 19:30:56| INFO |main.go:36Prediction session started
2025-08-11 19:30:56| WARN |main.go:39Low confidence for sample ID 123
2025-08-11 19:30:56| ERROR|main.go:41Failed to process sample ID 456
```

## Migration Guide

### From Previous Version
The logger maintains full backward compatibility:

**Before (still works):**
```go
logger.Info("Message")
logger.Debug("Debug message")
```

**New operation-specific capabilities:**
```go
// Training operations
logger.InitForTrain(verbose)
logger.TrainInfo("Training started")
logger.TrainError("Training failed")

// Prediction operations
logger.InitForPredict(verbose)
logger.PredictInfo("Prediction started")
logger.PredictError("Prediction failed")
```

## Benefits of SOLID Design

1. **Testability**: Easy to mock interfaces for unit testing
2. **Extensibility**: Add new encoders, writers, or config providers without changing existing code
3. **Maintainability**: Clear separation of concerns makes code easier to understand and modify
4. **Flexibility**: Dependency injection allows runtime configuration
5. **Backward Compatibility**: Existing code continues to work unchanged

## Configuration Options

The logger uses the following configuration settings:

- `LogFolder`: Directory for log files (default: "logs")
- `MaxSize`: Maximum size of log files in MB (default: 10, range: 1-1000)
- `EnableConsole`: Enable console output (default: true)
- `AppName`: Application name for log files (default: "mulberri")
- `EnableColors`: Enable colored console output (default: true)
- `Environment`: Development or Production (default: Development)
- `MaxBackups`: Number of backup files to keep (default: 7)
- `MaxAge`: Number of days to keep logs (default: 30)
- `Verbose`: Enable debug level logging (default: false)
- `Operation`: Operation type for log file naming (train/predict)

## Benefits for Berrijam AI Wrapper

2. **Date-Based Organization**: Logs are organized by date for easy historical analysis
3. **Automatic Management**: Old logs are automatically rotated and cleaned up
4. **Operation Separation**: Training and prediction logs are completely separate
5. **Predictable Naming**: Log files follow consistent naming patterns
6. **Space Efficient**: Backup files are compressed to save disk space
7. **Configurable Retention**: Adjust backup count and age limits as needed

## Advanced Usage

### Custom Log Rotation Settings
```go
// Create custom config with different rotation settings
config := logger.GetDefaultConfig()
config.MaxSize = 50      // 50MB files
config.MaxBackups = 10   // Keep 10 backups
config.MaxAge = 60       // Keep for 60 days
config.Operation = "train"
config.Verbose = true

// Use with factory pattern
configProvider := &logger.DefaultConfigProvider{Config: config}
factory := logger.NewLoggerFactory(configProvider, encoderFactory, writerFactory)
customLogger, err := factory.CreateLogger()
```

### Checking Current Settings
```go
// Check if verbose logging is enabled
if logger.IsVerboseEnabled() {
    // Perform debug-specific operations
}
```
