// Package logger provides a structured logging system for Mulberri with
// rotation, error tracking, and metadata support for decision tree operations.
//
// Architecture:
//   - logger.go: Core logging interfaces and implementations
//   - Config: Configuration management with validation
//   - Factories: Dependency injection for encoders and writers
//   - ZapLogger: High-performance logging using Uber's zap library
//
// Configuration:
//   - LogFolder: Directory for log files (default: "logs", must be writable)
//   - MaxSize: Log file size limit in MB (1-1000, default: 10)
//   - EnableConsole: Console output toggle (default: true)
//   - Verbose: Debug level logging (default: false, enables DEBUG level)
//
// Example:
//
//	logger.Info("Application started")
//	logger.Debug("Debug info") // Only shows when verbose=true
//
// Security: Log files contain application data, ensure proper file permissions.
// Performance: Uses zap for high-performance structured logging with minimal allocations.
// Dependencies: go.uber.org/zap v1.27+, lumberjack v2.2+ for rotation
package logger

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/buffer"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

// Default configuration constants for the logger.
const (
	// DefaultLogFolder is the logs directory for log files

	DefaultLogFolder = "logs"

	// DefaultMaxSize is the default maximum log file size in MB
	// Range: 1-1000 MB, balances disk usage and rotation frequency
	DefaultMaxSize = 10

	// DefaultEnableConsole enables console output by default
	// Set to false for production deployments to reduce I/O overhead
	DefaultEnableConsole = true

	// DefaultAppName is the default application name for log file naming
	// Used in log file names: {AppName}-{operation}.log
	DefaultAppName = "mulberri"

	// DefaultEnableColors enables colored console output by default
	// Automatically disabled when output is not a TTY
	DefaultEnableColors = true
)

// Operation types for different log files
const (
	// OperationTrain creates mulberri-train.log
	OperationTrain = "train"

	// OperationPredict creates mulberri-predict.log
	OperationPredict = "predict"
)

// Interfaces for SOLID compliance

// Logger defines the core logging interface (Interface Segregation Principle).
//
// All methods are thread-safe and non-blocking except Fatal which terminates the process.
// Implementations should handle context cancellation gracefully.
type Logger interface {
	// Debug logs debug-level messages (only visible when verbose=true)
	Debug(msg string)

	// Info logs informational messages for general application flow
	Info(msg string)

	// Warn logs warning messages for potentially harmful situations
	Warn(msg string)

	// Error logs error messages for error conditions that don't terminate the app
	Error(msg string)

	// Fatal logs fatal messages and terminates the application (calls os.Exit)
	Fatal(msg string)

	// Close flushes any buffered log entries and releases resources
	Close()
}

// ConfigProvider provides configuration for the logger (Dependency Inversion Principle).
//
// Implementations should validate configuration values and provide safe defaults.
// GetConfig should be idempotent and thread-safe.
type ConfigProvider interface {
	// GetConfig returns the current logger configuration
	// Returns validated config with safe defaults for missing values
	GetConfig() Config
}

// EncoderFactory creates encoders for different output types (Open/Closed Principle).
//
// Implementations should create thread-safe encoders optimized for their target output.
// Encoders must handle concurrent access from multiple goroutines.
type EncoderFactory interface {
	// CreateConsoleEncoder creates an encoder for console output
	// enableColors: true for colored output, false for plain text
	CreateConsoleEncoder(enableColors bool) zapcore.Encoder

	// CreateFileEncoder creates an encoder optimized for file output
	// Should produce structured, parseable output without colors
	CreateFileEncoder() zapcore.Encoder
}

// WriterFactory creates writers for different output destinations (Single Responsibility).
//
// Writers should handle rotation, compression, and cleanup automatically.
// All writers must be thread-safe for concurrent logging.
type WriterFactory interface {
	// CreateFileWriter creates a rotating file writer with the given configuration
	// Handles log rotation, compression, and old file cleanup automatically
	CreateFileWriter(config Config) *lumberjack.Logger
}

// Level represents the severity level of a log message (for backward compatibility).
//
// Constraints: Values 0-4 corresponding to debug through fatal levels.
// Relationships: Used with ParseLevel for string-to-level conversion.
type Level int

// Log levels in increasing order of severity (for backward compatibility).
//
// LevelDebug: Detailed information for debugging (verbose mode only)
// LevelInfo: General application flow information
// LevelWarning: Potentially harmful situations that don't stop execution
// LevelError: Error events that don't terminate the application
// LevelFatal: Severe errors that cause application termination
const (
	LevelDebug   Level = iota // 0: Debug information (verbose only)
	LevelInfo                 // 1: General information
	LevelWarning              // 2: Warning conditions
	LevelError                // 3: Error conditions
	LevelFatal                // 4: Fatal errors (terminates app)
)

// String returns the string representation of a log Level (for backward compatibility).
//
// Returns formatted level strings with consistent spacing for log alignment.
// Default case returns "UNKNOWN" for invalid levels.
func (l Level) String() string {
	switch l {
	case LevelDebug:
		return "DEBUG  "
	case LevelInfo:
		return "INFO   "
	case LevelWarning:
		return "WARN   "
	case LevelError:
		return "ERROR  "
	case LevelFatal:
		return "FATAL  "
	default:
		return "UNKNOWN"
	}
}

// ParseLevel parses a string and returns the corresponding Level (for backward compatibility).
//
// Args:
//   - levelStr: Level name (case-insensitive, whitespace trimmed)
//
// Accepts: "DEBUG", "INFO", "WARN"/"WARNING", "ERROR", "FATAL"
// Returns LevelInfo for unrecognized strings (safe default).
//
// Example: level := ParseLevel("DEBUG") // Returns LevelDebug
func ParseLevel(levelStr string) Level {
	switch strings.ToUpper(strings.TrimSpace(levelStr)) {
	case "DEBUG":
		return LevelDebug
	case "INFO":
		return LevelInfo
	case "WARN", "WARNING":
		return LevelWarning
	case "ERROR":
		return LevelError
	case "FATAL":
		return LevelFatal
	default:
		return LevelInfo
	}
}

// Environment represents the application environment for logger behavior.
//
// Constraints: Must be "development" or "production"
// Side effects: Changes log format (console vs JSON), caller info, stack traces
type Environment string

const (
	// Development enables console format, caller info, and stack traces
	Development Environment = "development"
	// Production enables JSON format, minimal caller info, no stack traces
	Production Environment = "production"
)

// Config holds logger configuration options with validation constraints.
//
// Security: LogFolder must have appropriate write permissions for the application.
// Relationships: Used by ConfigProvider implementations and LoggerFactory.
// Side effects: Changes affect all new logger instances created with this config.
type Config struct {
	// LogFolder is the directory for log files (current directory by default)
	LogFolder string

	// MaxSize is the maximum log file size in MB (range: 1-1000)
	MaxSize int64

	// EnableConsole enables/disables console output (false reduces I/O in production)
	EnableConsole bool

	// AppName is used in log file naming: {AppName}-{operation}.log
	AppName string

	// EnableColors enables colored console output (auto-disabled for non-TTY)
	EnableColors bool

	// Environment controls log format and verbosity (Development/Production)
	Environment Environment

	// MaxBackups is the number of old log files to retain (0 = disable rotation)
	MaxBackups int

	// MaxAge is the maximum age in days to retain log files (0 = no age limit)
	MaxAge int

	// Verbose enables debug level logging when true (false = info level and above)
	Verbose bool

	// Operation specifies the operation type for log file naming (train/predict)
	Operation string
}

// ZapLogger wraps zap.Logger to implement the Logger interface.
//
// Security: Thread-safe for concurrent access from multiple goroutines.
// Performance: Uses zap's high-performance structured logging with minimal allocations.
// Relationships: Implements Logger interface, created by LoggerFactory.
type ZapLogger struct {
	// config stores the logger configuration (immutable after creation)
	config Config

	// zapLogger is the underlying zap logger instance (thread-safe)
	zapLogger *zap.Logger

	// sugar provides the sugared API for easier string formatting (thread-safe)
	sugar *zap.SugaredLogger
}

// Global logger instance and synchronization primitives.
//
// Security: Thread-safe access protected by globalMu mutex.
// Side effects: Lazy initialization on first access via getGlobalLogger().
var (
	// globalLogger is the singleton logger instance (nil until first access)
	globalLogger Logger

	// globalOnce ensures globalLogger is initialized exactly once
	globalOnce sync.Once

	// globalMu protects concurrent access to globalLogger
	globalMu sync.RWMutex
)

// getDefaultConfig returns a Config struct with default values.
//
// Returns a configuration suitable for development with safe defaults.
// All values are validated and within acceptable ranges.
// Verbose defaults to false (info level and above only).
// Operation defaults to empty string (will be set by specific functions).
func getDefaultConfig() Config {
	return Config{
		LogFolder:     DefaultLogFolder,     // logs directory
		MaxSize:       DefaultMaxSize,       // 10 MB per file
		EnableConsole: DefaultEnableConsole, // Console output enabled
		AppName:       DefaultAppName,       // "mulberri" app name
		EnableColors:  DefaultEnableColors,  // Colors enabled
		Environment:   Development,          // Development mode
		MaxBackups:    7,                    // Keep 7 backup files
		MaxAge:        7,                   // Keep files for 7 days
		Verbose:       false,                // Default: info level and above only
		Operation:     "",                   // Will be set by InitForTrain/InitForPredict
	}
}

// Implementation of interfaces following SOLID principles

// DefaultConfigProvider implements ConfigProvider interface with validated defaults.
//
// Security: Thread-safe for concurrent access, immutable after creation.
// Relationships: Implements ConfigProvider, used by LoggerFactory.
type DefaultConfigProvider struct {
	// config stores the validated configuration (immutable after creation)
	config Config
}

// NewDefaultConfigProvider creates a new DefaultConfigProvider with verbose setting.
//
// Args:
//   - verbose: Enable debug level logging (true) or info level and above (false)
//
// Returns ConfigProvider with validated defaults and specified verbose setting.
// Configuration is immutable after creation for thread safety.
//
// Example: provider := NewDefaultConfigProvider(true) // Debug logging enabled
func NewDefaultConfigProvider(verbose bool) ConfigProvider {
	config := getDefaultConfig()
	config.Verbose = verbose
	return &DefaultConfigProvider{
		config: config,
	}
}

// GetConfig returns the configuration.
//
// Returns the immutable configuration set during provider creation.
// Thread-safe for concurrent access, always returns the same values.
func (cp *DefaultConfigProvider) GetConfig() Config {
	return cp.config
}

// DefaultEncoderFactory implements EncoderFactory interface with custom formatters.
//
// Performance: Creates optimized encoders for console and file output.
// Relationships: Implements EncoderFactory, used by LoggerFactory.
type DefaultEncoderFactory struct{}

// NewDefaultEncoderFactory creates a new DefaultEncoderFactory.
//
// Returns a factory that creates custom encoders with consistent formatting.
// Factory is stateless and thread-safe for concurrent use.
func NewDefaultEncoderFactory() EncoderFactory {
	return &DefaultEncoderFactory{}
}

// CreateConsoleEncoder creates a console encoder with optional colors.
//
// Args:
//   - enableColors: true for ANSI colored output, false for plain text
//
// Returns thread-safe encoder optimized for human-readable console output.
// Colors are automatically disabled for non-TTY outputs.
func (ef *DefaultEncoderFactory) CreateConsoleEncoder(enableColors bool) zapcore.Encoder {
	if enableColors {
		return newCustomConsoleEncoder()
	}
	return newCustomConsoleEncoderNoColors()
}

// CreateFileEncoder creates a file encoder optimized for structured logging.
//
// Returns thread-safe encoder that produces parseable output without colors.
// Format: timestamp | level | file:function:line | message
func (ef *DefaultEncoderFactory) CreateFileEncoder() zapcore.Encoder {
	return newCustomFileEncoder()
}

// DefaultWriterFactory implements WriterFactory interface with log rotation.
//
// Security: Creates files with appropriate permissions (0644).
// Performance: Handles automatic rotation, compression, and cleanup.
// Relationships: Implements WriterFactory, used by LoggerFactory.
type DefaultWriterFactory struct{}

// NewDefaultWriterFactory creates a new DefaultWriterFactory.
//
// Returns a factory that creates lumberjack writers with rotation support.
// Factory is stateless and thread-safe for concurrent use.
func NewDefaultWriterFactory() WriterFactory {
	return &DefaultWriterFactory{}
}

// CreateFileWriter creates a lumberjack writer for log rotation.
//
// Args:
//   - config: Configuration with file settings (LogFolder, MaxSize, etc.)
//
// Returns writer that handles automatic rotation when size limits are reached.
// Files are compressed and cleaned up according to MaxBackups and MaxAge settings.
//
// Side effects: Creates log directory if it doesn't exist.
func (wf *DefaultWriterFactory) CreateFileWriter(config Config) *lumberjack.Logger {
	return createLumberjackWriter(config)
}

// LoggerFactory creates loggers using dependency injection (Dependency Inversion Principle).
//
// Security: Thread-safe for concurrent logger creation.
// Relationships: Orchestrates ConfigProvider, EncoderFactory, and WriterFactory.
// Side effects: Creates log directories and files as needed.
type LoggerFactory struct {
	// configProvider supplies logger configuration (injected dependency)
	configProvider ConfigProvider

	// encoderFactory creates output encoders (injected dependency)
	encoderFactory EncoderFactory

	// writerFactory creates file writers (injected dependency)
	writerFactory WriterFactory
}

// NewLoggerFactory creates a new LoggerFactory with injected dependencies.
//
// Args:
//   - cp: ConfigProvider for logger configuration
//   - ef: EncoderFactory for creating output encoders
//   - wf: WriterFactory for creating file writers
//
// Returns factory configured with the provided dependencies.
// All dependencies must be non-nil and thread-safe.
//
// Example: factory := NewLoggerFactory(configProvider, encoderFactory, writerFactory)
func NewLoggerFactory(cp ConfigProvider, ef EncoderFactory, wf WriterFactory) *LoggerFactory {
	return &LoggerFactory{
		configProvider: cp,
		encoderFactory: ef,
		writerFactory:  wf,
	}
}

// CreateLogger creates a new logger using the injected dependencies.
//
// Returns fully configured Logger instance with file rotation and console output.
// Each call creates a new logger instance with the current configuration.
//
// Side effects: Creates log directory if it doesn't exist, may create log files.
// Returns error if directory creation fails or configuration is invalid.
func (lf *LoggerFactory) CreateLogger() (Logger, error) {
	config := lf.configProvider.GetConfig()
	return NewWithConfigAndFactories(config, lf.encoderFactory, lf.writerFactory)
}

// NewWithConfigAndFactories creates a new Logger with the given configuration and factories.
//
// Args:
//   - config: Logger configuration with validated settings
//   - encoderFactory: Factory for creating output encoders
//   - writerFactory: Factory for creating file writers
//
// Returns fully configured Logger with file rotation and optional console output.
// Supports both file and console outputs with different formatting.
//
// Side effects: Creates log directory (0755 permissions) if it doesn't exist.
// Returns error if directory creation fails or factories return invalid components.
//
// Example: logger, err := NewWithConfigAndFactories(config, encoderFactory, writerFactory)
func NewWithConfigAndFactories(config Config, encoderFactory EncoderFactory, writerFactory WriterFactory) (Logger, error) {
	// Create log directory with appropriate permissions
	if err := os.MkdirAll(config.LogFolder, 0755); err != nil {
		return nil, fmt.Errorf("failed to create log directory: %w", err)
	}

	// Create zap configuration based on environment and verbose settings
	zapConfig := createZapConfig(config)

	// Create cores for different outputs (file + optional console)
	var cores []zapcore.Core

	// File output with automatic rotation and compression
	fileWriter := writerFactory.CreateFileWriter(config)
	fileEncoder := encoderFactory.CreateFileEncoder()
	fileCore := zapcore.NewCore(fileEncoder, zapcore.AddSync(fileWriter), zapConfig.Level)
	cores = append(cores, fileCore)

	// Console output (if enabled in configuration)
	if config.EnableConsole {
		consoleEncoder := encoderFactory.CreateConsoleEncoder(config.EnableColors)
		consoleCore := zapcore.NewCore(consoleEncoder, zapcore.AddSync(os.Stdout), zapConfig.Level)
		cores = append(cores, consoleCore)
	}

	// Combine all cores for simultaneous output
	core := zapcore.NewTee(cores...)

	// Create logger with caller information and appropriate skip level
	zapLogger := zap.New(core, zap.AddCaller(), zap.AddCallerSkip(2))

	return &ZapLogger{
		config:    config,
		zapLogger: zapLogger,
		sugar:     zapLogger.Sugar(),
	}, nil
}

// customConsoleEncoder creates a custom encoder that formats logs exactly as specified
type customConsoleEncoder struct {
	zapcore.Encoder
	enableColors bool
}

func newCustomConsoleEncoder() zapcore.Encoder {
	return &customConsoleEncoder{enableColors: true}
}

func newCustomConsoleEncoderNoColors() zapcore.Encoder {
	return &customConsoleEncoder{enableColors: false}
}

// customFileEncoder creates a custom encoder for file output (same format as console but no colors)
type customFileEncoder struct {
	zapcore.Encoder
}

func newCustomFileEncoder() zapcore.Encoder {
	return &customFileEncoder{}
}

func (enc *customFileEncoder) Clone() zapcore.Encoder {
	return &customFileEncoder{}
}

func (enc *customFileEncoder) EncodeEntry(entry zapcore.Entry, fields []zapcore.Field) (*buffer.Buffer, error) {
	buf := buffer.NewPool().Get()

	// Time
	buf.AppendString(entry.Time.Format("2006-01-02 15:04:05"))
	buf.AppendString("| ")

	// Level with proper spacing (file encoder - no colors)
	var levelStr string
	switch entry.Level {
	case zapcore.DebugLevel:
		levelStr = "DEBUG"
	case zapcore.InfoLevel:
		levelStr = "INFO "
	case zapcore.WarnLevel:
		levelStr = "WARN "
	case zapcore.ErrorLevel:
		levelStr = "ERROR"
	case zapcore.FatalLevel:
		levelStr = "FATAL"
	default:
		levelStr = "UNKNOWN"
	}
	buf.AppendString(levelStr)
	buf.AppendString("|")

	// Caller info
	if entry.Caller.Defined {
		// Get the function name from the caller
		funcName := entry.Caller.Function
		if funcName != "" {
			// Extract just the function name (remove package path)
			if idx := strings.LastIndex(funcName, "."); idx >= 0 {
				funcName = funcName[idx+1:]
			}
		} else {
			funcName = "unknown"
		}

		// Get the file name (remove directory path)
		fileName := entry.Caller.File
		if idx := strings.LastIndex(fileName, "/"); idx >= 0 {
			fileName = fileName[idx+1:]
		}

		buf.AppendString(fmt.Sprintf("%s:%s:%d", fileName, funcName, entry.Caller.Line))
	}
	buf.AppendString(" | ")

	// Message
	buf.AppendString(entry.Message)
	buf.AppendString("\n")

	return buf, nil
}

func (enc *customConsoleEncoder) Clone() zapcore.Encoder {
	return &customConsoleEncoder{enableColors: enc.enableColors}
}

func (enc *customConsoleEncoder) EncodeEntry(entry zapcore.Entry, fields []zapcore.Field) (*buffer.Buffer, error) {
	buf := buffer.NewPool().Get()

	// Time
	buf.AppendString("\033[36m")
	buf.AppendString(entry.Time.Format("2006-01-02 15:04:05"))
	buf.AppendString("\033[0m")
	buf.AppendString("| ")

	// Level with proper spacing and optional colors
	var levelStr string
	if enc.enableColors {
		// Add colors for different log levels
		switch entry.Level {
		case zapcore.DebugLevel:
			levelStr = "\033[35mDEBUG\033[0m"
		case zapcore.InfoLevel:
			levelStr = "\033[32mINFO \033[0m"
		case zapcore.WarnLevel:
			levelStr = "\033[33mWARN \033[0m"
		case zapcore.ErrorLevel:
			levelStr = "\033[31mERROR\033[0m"
		case zapcore.FatalLevel:
			levelStr = "\033[31mFATAL\033[0m"
		default:
			levelStr = "UNKNOWN"
		}
	} else {
		// No colors
		switch entry.Level {
		case zapcore.DebugLevel:
			levelStr = "DEBUG"
		case zapcore.InfoLevel:
			levelStr = "INFO "
		case zapcore.WarnLevel:
			levelStr = "WARN "
		case zapcore.ErrorLevel:
			levelStr = "ERROR"
		case zapcore.FatalLevel:
			levelStr = "FATAL"
		default:
			levelStr = "UNKNOWN"
		}
	}
	buf.AppendString(levelStr)
	buf.AppendString("|")

	// Caller info
	if entry.Caller.Defined {
		// Get the function name from the caller
		funcName := entry.Caller.Function
		if funcName != "" {
			// Extract just the function name (remove package path)
			if idx := strings.LastIndex(funcName, "."); idx >= 0 {
				funcName = funcName[idx+1:]
			}
		} else {
			funcName = "unknown"
		}

		// Get the file name (remove directory path)
		fileName := entry.Caller.File
		if idx := strings.LastIndex(fileName, "/"); idx >= 0 {
			fileName = fileName[idx+1:]
		}

		buf.AppendString(fmt.Sprintf("%s:%s:%d", fileName, funcName, entry.Caller.Line))
	}
	buf.AppendString(" | ")

	// Message
	buf.AppendString(entry.Message)
	buf.AppendString("\n")

	return buf, nil
}

// createZapConfig creates a zap configuration based on environment and config
func createZapConfig(config Config) zap.Config {
	var zapConfig zap.Config

	if config.Environment == Production {
		// Production: JSON format, no caller info by default, structured
		zapConfig = zap.NewProductionConfig()
		zapConfig.DisableCaller = false // Enable caller info as requested
		zapConfig.DisableStacktrace = true
	} else {
		// Development: Console format, caller info, more verbose
		zapConfig = zap.NewDevelopmentConfig()
		zapConfig.DisableCaller = false
		zapConfig.DisableStacktrace = false
	}

	// Set log level based on verbose flag
	if config.Verbose {
		zapConfig.Level = zap.NewAtomicLevelAt(zapcore.DebugLevel)
	} else {
		zapConfig.Level = zap.NewAtomicLevelAt(zapcore.InfoLevel)
	}

	// Set encoding based on environment and color preference
	if config.Environment == Development && config.EnableColors {
		zapConfig.Encoding = "console"
		zapConfig.EncoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder
	} else if config.Environment == Development {
		zapConfig.Encoding = "console"
		zapConfig.EncoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder
	} else {
		zapConfig.Encoding = "json"
	}

	// Configure time format for file output (JSON)
	zapConfig.EncoderConfig.TimeKey = "timestamp"
	zapConfig.EncoderConfig.EncodeTime = zapcore.TimeEncoderOfLayout("2006-01-02 15:04:05.000")
	zapConfig.EncoderConfig.CallerKey = "caller"
	zapConfig.EncoderConfig.EncodeCaller = zapcore.ShortCallerEncoder

	return zapConfig
}

// createLumberjackWriter creates a lumberjack writer for operation-specific log files.
// Creates files in logs directory with format: {AppName}-{operation}-{YYYY-MM-DD}.log
// Enables rotation based on size, backup count, and age limits.
func createLumberjackWriter(config Config) *lumberjack.Logger {
	// Use operation-specific filename with date: mulberri-train-2025-08-11.log
	operation := config.Operation
	if operation == "" {
		operation = "app" // Fallback for general usage
	}

	// Add current date to filename for daily log separation
	currentDate := time.Now().Format("2006-01-02")
	logFile := filepath.Join(config.LogFolder, fmt.Sprintf("%s-%s-%s.log", config.AppName, operation, currentDate))

	return &lumberjack.Logger{
		Filename:   logFile,
		MaxSize:    int(config.MaxSize), // MB - rotate when file reaches this size
		MaxBackups: config.MaxBackups,   // Number of backup files to keep
		MaxAge:     config.MaxAge,       // Days to keep old log files
		Compress:   true,                // Compress rotated files to save space
	}
}

// NewLoggerWithVerbose creates a logger using the factory pattern with dependency injection.
//
// Args:
//   - verbose: Enable debug level logging (true) or info level and above (false)
//
// Returns Logger created through full dependency injection pattern.
// Demonstrates proper SOLID principles usage with all factories.
//
// Example: logger, err := NewLoggerWithVerbose(true) // Factory pattern with DI
func NewLoggerWithVerbose(verbose bool) (Logger, error) {
	// call verbose variable from cli package here and parse it to the function NewDefaultConfigProvider
	configProvider := NewDefaultConfigProvider(verbose)
	encoderFactory := NewDefaultEncoderFactory()
	writerFactory := NewDefaultWriterFactory()

	factory := NewLoggerFactory(configProvider, encoderFactory, writerFactory)
	return factory.CreateLogger()
}

// Debug logs a debug-level message.
//
// Args:
//   - msg: Debug message for detailed troubleshooting information
//
// Only outputs when verbose=true in configuration.
// Thread-safe for concurrent access from multiple goroutines.
func (l *ZapLogger) Debug(msg string) {
	l.sugar.Debug(msg)
}

// Info logs an info-level message.
//
// Args:
//   - msg: Informational message about general application flow
//
// Always outputs unless log level is set higher than info.
// Thread-safe for concurrent access from multiple goroutines.
func (l *ZapLogger) Info(msg string) {
	l.sugar.Info(msg)
}

// Warn logs a warning-level message.
//
// Args:
//   - msg: Warning message for potentially harmful situations
//
// Indicates issues that don't prevent continued operation.
// Thread-safe for concurrent access from multiple goroutines.
func (l *ZapLogger) Warn(msg string) {
	l.sugar.Warn(msg)
}

// Error logs an error-level message.
//
// Args:
//   - msg: Error message for error conditions that don't terminate the app
//
// Indicates errors that should be investigated but don't stop execution.
// Thread-safe for concurrent access from multiple goroutines.
func (l *ZapLogger) Error(msg string) {
	l.sugar.Error(msg)
}

// Fatal logs a fatal-level message and exits.
//
// Args:
//   - msg: Fatal error message before application termination
//
// Side effects: Calls os.Exit(1) after logging, terminating the application.
// Use only for unrecoverable errors that require immediate shutdown.
func (l *ZapLogger) Fatal(msg string) {
	l.sugar.Fatal(msg)
}

// Close closes the logger and syncs any buffered log entries.
//
// Flushes any pending log entries to ensure all messages are written.
// Should be called before application shutdown, typically with defer.
//
// Example: defer logger.Close()
func (l *ZapLogger) Close() {
	if l.zapLogger != nil {
		l.zapLogger.Sync()
	}
}

// initGlobalLogger initializes the global logger with default config.
func initGlobalLogger() {
	config := getDefaultConfig()
	initGlobalLoggerWithConfig(config)
}

// initGlobalLoggerWithConfig initializes the global logger with a given config.
func initGlobalLoggerWithConfig(config Config) {
	// Use factory pattern with the provided config
	configProvider := &DefaultConfigProvider{config: config}
	encoderFactory := NewDefaultEncoderFactory()
	writerFactory := NewDefaultWriterFactory()
	factory := NewLoggerFactory(configProvider, encoderFactory, writerFactory)

	var err error
	globalLogger, err = factory.CreateLogger()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to initialize global logger: %v\n", err)
		os.Exit(1)
	}
}

// getGlobalLogger returns the singleton global logger, initializing if needed.
func getGlobalLogger() Logger {
	globalMu.RLock()
	if globalLogger != nil {
		defer globalMu.RUnlock()
		return globalLogger
	}
	globalMu.RUnlock()

	globalMu.Lock()
	defer globalMu.Unlock()
	if globalLogger == nil {
		globalOnce.Do(initGlobalLogger)
	}
	return globalLogger
}

// Global convenience functions that maintain the original API for backward compatibility.
//
// These functions use the singleton global logger with lazy initialization.
// Thread-safe for concurrent access, automatically initialize on first use.

// Debug logs a debug-level message using the global logger.
//
// Args:
//   - msg: Debug message (only visible when global logger has verbose=true)
//
// Backward compatible convenience function for simple logging.
func Debug(msg string) { getGlobalLogger().Debug(msg) }

// Info logs an info-level message using the global logger.
//
// Args:
//   - msg: Informational message about application flow
//
// Backward compatible convenience function for simple logging.
func Info(msg string) { getGlobalLogger().Info(msg) }

// Warn logs a warning-level message using the global logger.
//
// Args:
//   - msg: Warning message for potentially harmful situations
//
// Backward compatible convenience function for simple logging.
func Warn(msg string) { getGlobalLogger().Warn(msg) }

// Error logs an error-level message using the global logger.
//
// Args:
//   - msg: Error message for non-fatal error conditions
//
// Backward compatible convenience function for simple logging.
func Error(msg string) { getGlobalLogger().Error(msg) }

// Fatal logs a fatal-level message using the global logger and exits.
//
// Args:
//   - msg: Fatal error message before application termination
//
// Side effects: Terminates application with os.Exit(1) after logging.
// Backward compatible convenience function for fatal errors.
func Fatal(msg string) { getGlobalLogger().Fatal(msg) }

// Training-specific logging functions
//
// logs go to the training log file when InitForTrain() has been called.

// TrainDebug logs a debug-level message for training operations.
//
// Args:
//   - msg: Debug message related to training process
//
// Example: TrainDebug("Learning rate adjusted to 0.001")
func TrainDebug(msg string) { getGlobalLogger().Debug( msg) }

// TrainInfo logs an info-level message for training operations.
//
// Args:
//   - msg: Informational message about training progress
//
// Example: TrainInfo("Epoch 5 completed successfully")
func TrainInfo(msg string) { getGlobalLogger().Info( msg) }

// TrainWarn logs a warning-level message for training operations.
//
// Args:
//   - msg: Warning message about training issues
//
// Example: TrainWarn("Training accuracy below expected threshold")
func TrainWarn(msg string) { getGlobalLogger().Warn(msg) }

// TrainError logs an error-level message for training operations.
//
// Args:
//   - msg: Error message about training failures
//
// Example: TrainError("Failed to load training data batch")
func TrainError(msg string) { getGlobalLogger().Error( msg) }

// TrainFatal logs a fatal-level message for training operations and exits.
//
// Args:
//   - msg: Fatal error message before application termination
//
// Side effects: Terminates application with os.Exit(1) after logging.
// Example: TrainFatal("Critical training error: out of memory")
func TrainFatal(msg string) { getGlobalLogger().Fatal( msg) }

// Prediction-specific logging functions
//
// logs go to the prediction log file when InitForPredict() has been called.

// PredictDebug logs a debug-level message for prediction operations.
//
// Args:
//   - msg: Debug message related to prediction process
//
// Example: PredictDebug("Model confidence score: 0.95")
func PredictDebug(msg string) { getGlobalLogger().Debug( msg) }

// PredictInfo logs an info-level message for prediction operations.
//
// Args:
//   - msg: Informational message about prediction progress
//
// Automatically prefixed with [PREDICT] for easy identification.
// Example: PredictInfo("Processing 1000 samples for prediction")
func PredictInfo(msg string) { getGlobalLogger().Info(msg) }

// PredictWarn logs a warning-level message for prediction operations.
//
// Args:
//   - msg: Warning message about prediction issues
//
// Automatically prefixed with [PREDICT] for easy identification.
// Example: PredictWarn("Low confidence prediction for sample ID 123")
func PredictWarn(msg string) { getGlobalLogger().Warn(msg) }

// PredictError logs an error-level message for prediction operations.
//
// Args:
//   - msg: Error message about prediction failures
//
// Automatically prefixed with [PREDICT] for easy identification.
// Example: PredictError("Failed to process sample ID 456")
func PredictError(msg string) { getGlobalLogger().Error(msg) }

// PredictFatal logs a fatal-level message for prediction operations and exits.
//
// Args:
//   - msg: Fatal error message before application termination
//
// Side effects: Terminates application with os.Exit(1) after logging.
// Automatically prefixed with [PREDICT] for easy identification.
// Example: PredictFatal("Critical prediction error: model not found")
func PredictFatal(msg string) { getGlobalLogger().Fatal(msg) }

// InitWithVerbose initializes the global logger with verbose flag support.
//
// Args:
//   - verbose: Enable debug level logging (true) or info level and above (false)
//
// Should be called early in application lifecycle, typically in main().
// Replaces any existing global logger configuration.
//
// Example: logger.InitWithVerbose(true) // Enable debug logging globally
func InitWithVerbose(verbose bool) error {
	return SetGlobalConfigWithVerbose(verbose)
}

// InitForTrain initializes the global logger for training operations.
//
// Args:
//   - verbose: Enable debug level logging (true) or info level and above (false)
//
// Creates logs/mulberri-train-YYYY-MM-DD.log with rotation support.
// Rotates when file reaches 10MB, keeps 7 backups, retains files for 7 days.
//
// Example: logger.InitForTrain(cli.verbose) // Called by CLI package for train command
func InitForTrain(verbose bool) error {
	return initForOperation(OperationTrain, verbose)
}

// InitForPredict initializes the global logger for prediction operations.
//
// Args:
//   - verbose: Enable debug level logging (true) or info level and above (false)
//
// Creates logs/mulberri-predict-YYYY-MM-DD.log with rotation support.
// Rotates when file reaches 10MB, keeps 7 backups, retains files for 7 days.
//
// Example: logger.InitForPredict(cli.verbose) // Called by CLI package for predict command
func InitForPredict(verbose bool) error {
	return initForOperation(OperationPredict, verbose)
}

// initForOperation initializes the global logger for a specific operation.
//
// Args:
//   - operation: The operation type (train/predict) for log file naming
//   - verbose: Enable debug level logging (true) or info level and above (false)
//
// Creates operation-specific log files with date and rotation support.
// Files: logs/{AppName}-{operation}-{YYYY-MM-DD}.log
// Rotation: 10MB max size, 7 backups, 7 days retention, compressed backups.
// Thread-safe: Protected by mutex for concurrent access.
func initForOperation(operation string, verbose bool) error {
	globalMu.Lock()
	defer globalMu.Unlock()

	if globalLogger != nil {
		globalLogger.Close()
	}

	// Create config with operation-specific settings
	config := getDefaultConfig()
	config.Operation = operation
	config.Verbose = verbose

	// Use factory pattern with operation-specific config
	configProvider := &DefaultConfigProvider{config: config}
	encoderFactory := NewDefaultEncoderFactory()
	writerFactory := NewDefaultWriterFactory()
	factory := NewLoggerFactory(configProvider, encoderFactory, writerFactory)

	var err error
	globalLogger, err = factory.CreateLogger()
	return err
}

// IsVerboseEnabled returns whether verbose (debug) logging is currently enabled.
//
// Returns true if the global logger is configured for debug level logging,
// false if it's configured for info level and above only.
//
// Thread-safe: Protected by mutex for concurrent access.
// Returns false if global logger hasn't been initialized yet.
//
// Example: if logger.IsVerboseEnabled() { /* debug-specific logic */ }
func IsVerboseEnabled() bool {
	globalMu.RLock()
	defer globalMu.RUnlock()

	if globalLogger == nil {
		return false // Default is non-verbose
	}

	// Check if the logger is a ZapLogger and get its config
	if zapLogger, ok := globalLogger.(*ZapLogger); ok {
		return zapLogger.config.Verbose
	}

	return false // Safe default
}

// SetGlobalConfig sets the global logger configuration.
func SetGlobalConfig(config Config) error {
	globalMu.Lock()
	defer globalMu.Unlock()

	if globalLogger != nil {
		globalLogger.Close()
	}

	// Use factory pattern with the provided config
	configProvider := &DefaultConfigProvider{config: config}
	encoderFactory := NewDefaultEncoderFactory()
	writerFactory := NewDefaultWriterFactory()
	factory := NewLoggerFactory(configProvider, encoderFactory, writerFactory)

	var err error
	globalLogger, err = factory.CreateLogger()
	return err
}

// SetGlobalConfigWithVerbose sets the global logger config with verbose flag.
func SetGlobalConfigWithVerbose(verbose bool) error {
	globalMu.Lock()
	defer globalMu.Unlock()

	if globalLogger != nil {
		globalLogger.Close()
	}

	// Use factory pattern with dependency injection
	configProvider := NewDefaultConfigProvider(verbose)
	encoderFactory := NewDefaultEncoderFactory()
	writerFactory := NewDefaultWriterFactory()
	factory := NewLoggerFactory(configProvider, encoderFactory, writerFactory)

	var err error
	globalLogger, err = factory.CreateLogger()
	return err
}

// CloseGlobal closes the global logger.
func CloseGlobal() {
	globalMu.Lock()
	defer globalMu.Unlock()
	if globalLogger != nil {
		globalLogger.Close()
		globalLogger = nil
	}
}
