package main

import (
	"github.com/berrijam/mulberri/internal/utils/logger"
)

func main() {
	// Test training operations
	logger.InitForTrain(true)
	logger.TrainInfo("Training session started")
	logger.TrainDebug("Loading dataset with 10,000 samples")
	logger.TrainWarn("Learning rate might be too high")
	
	// Test prediction operations
	logger.InitForPredict(false)
	logger.PredictInfo("Prediction session started")
	logger.PredictDebug("Model confidence: 0.95") // Won't show (non-verbose)
	logger.PredictWarn("Low confidence for sample ID 123")
	
	// Test general functions
	logger.Info("General application message")
	logger.Debug("General debug message") // Won't show (non-verbose)
}
